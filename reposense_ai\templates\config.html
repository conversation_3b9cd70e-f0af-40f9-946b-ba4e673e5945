{% extends "base.html" %}

{% block title %}Configuration - RepoSense AI{% endblock %}

{% block content %}
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="page-title">Configuration</h1>
            <p class="page-subtitle">Configure global settings for repository intelligence and private AI integration
            </p>
        </div>
        <button type="button" class="btn btn-outline-secondary" onclick="testOllamaConnection(this)">
            <i class="fas fa-plug"></i> Test Ollama Connection
        </button>
    </div>
</div>

<div class="row">
    <div class="col-md-8 offset-md-2">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-cog"></i>Configuration</h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('save_config') }}" id="configurationForm"
                    aria-label="RepoSense AI Configuration Settings" data-single-action="save-all-configuration">
                    <!-- SVN Server Configuration -->
                    <div class="card mb-4">
                        <div class="card-header bg-light d-flex justify-content-between align-items-center">
                            <h6 class="mb-0"><i class="fas fa-server"></i> SVN Server Configuration</h6>
                            <a href="{{ url_for('repository_discovery_page') }}" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-search"></i> Discover Repositories
                            </a>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label for="server_name" class="form-label">Server Name</label>
                                <input type="text" class="form-control" id="server_name" name="server_name"
                                    value="{{ config.server.name if config.server else 'default' }}"
                                    placeholder="default" pattern="[a-zA-Z0-9_\-]+"
                                    title="Only letters, numbers, underscores, and hyphens allowed">
                                <div class="form-text">Friendly name for this server (used in document IDs). Only
                                    letters, numbers, underscores, and hyphens allowed.</div>
                            </div>
                            <div class="mb-3">
                                <label for="server_description" class="form-label">Server Description</label>
                                <input type="text" class="form-control" id="server_description"
                                    name="server_description"
                                    value="{{ config.server.description if config.server else '' }}"
                                    placeholder="Optional description">
                                <div class="form-text">Optional description for this server</div>
                            </div>
                            <div class="mb-3">
                                <label for="svn_server_url" class="form-label">SVN Server Base URL</label>
                                <input type="url" class="form-control" id="svn_server_url" name="svn_server_url"
                                    value="{{ config.svn_server_url or '' }}" placeholder="http://your-server:port/svn">
                                <div class="form-text">Base URL of your SVN server (used for repository discovery)</div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="svn_server_username" class="form-label">Server Username</label>
                                        <input type="text" class="form-control" id="svn_server_username"
                                            name="svn_server_username" value="{{ config.svn_server_username or '' }}"
                                            placeholder="Optional" autocomplete="username">
                                        <div class="form-text">Default username for server access</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="svn_server_password" class="form-label">Server Password</label>
                                        <input type="password" class="form-control" id="svn_server_password"
                                            name="svn_server_password" value="{{ config.svn_server_password or '' }}"
                                            placeholder="Optional" autocomplete="current-password">
                                        <div class="form-text">Default password for server access</div>
                                    </div>
                                </div>
                            </div>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i>
                                <strong>Repository Management:</strong> Individual repositories are configured in the
                                <a href="{{ url_for('repositories_page') }}" class="alert-link">Repositories</a>
                                section.
                                Use the "Discover Repositories" button above to automatically find and import
                                repositories from your SVN server.
                            </div>
                        </div>
                    </div>

                    <!-- Ollama Configuration -->
                    <div class="card mb-4">
                        <div class="card-header bg-light">
                            <h6 class="mb-0"><i class="fas fa-robot"></i> Ollama AI</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-8">
                                    <div class="mb-3">
                                        <label for="ollama_host" class="form-label">Ollama Host</label>
                                        <input type="text" class="form-control" id="ollama_host" name="ollama_host"
                                            value="{{ config.ollama_host }}" required placeholder="http://ollama:11434">
                                        <div class="form-text">URL of the Ollama server</div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="ollama_model" class="form-label">Model</label>
                                        <div class="input-group">
                                            <select class="form-select" id="ollama_model" name="ollama_model">
                                                <option value="">
                                                    <i class="fas fa-spinner fa-spin"></i> Loading models...
                                                </option>
                                            </select>
                                            <button class="btn btn-outline-secondary" type="button" id="refresh-models"
                                                title="Refresh available models">
                                                <i class="fas fa-sync-alt"></i>
                                            </button>
                                        </div>
                                        <div class="form-text" id="model-status">Available models from your Ollama
                                            server</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Specialized AI Models Configuration -->
                    <div class="card mb-4">
                        <div class="card-header bg-light">
                            <h6 class="mb-0"><i class="fas fa-brain"></i> Specialized AI Models</h6>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i>
                                <strong>Optional:</strong> Configure specialized models for different AI tasks. If not
                                specified, the default model will be used for all tasks.
                            </div>

                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="ollama_model_documentation" class="form-label">Product Documentation
                                            Model</label>
                                        <select class="form-select specialized-model-select"
                                            id="ollama_model_documentation" name="ollama_model_documentation">
                                            <option value="">Use default model</option>
                                        </select>
                                        <div class="form-text">Model for analyzing user-facing documentation impact
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="ollama_model_code_review" class="form-label">Code Review
                                            Model</label>
                                        <select class="form-select specialized-model-select"
                                            id="ollama_model_code_review" name="ollama_model_code_review">
                                            <option value="">Use default model</option>
                                        </select>
                                        <div class="form-text">Model for code review analysis and recommendations</div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="ollama_model_risk_assessment" class="form-label">Risk Assessment
                                            Model</label>
                                        <select class="form-select specialized-model-select"
                                            id="ollama_model_risk_assessment" name="ollama_model_risk_assessment">
                                            <option value="">Use default model</option>
                                        </select>
                                        <div class="form-text">Model for security and risk analysis</div>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-12">
                                    <h6 class="mb-3"><i class="fas fa-cogs"></i> Enhanced Prompt Settings</h6>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="use_enhanced_prompts"
                                                name="use_enhanced_prompts" {{ 'checked' if config.use_enhanced_prompts
                                                else '' }}>
                                            <label class="form-check-label" for="use_enhanced_prompts">
                                                <strong>Use Enhanced Contextual Prompts</strong>
                                            </label>
                                        </div>
                                        <div class="form-text">Enable advanced contextual prompts that analyze
                                            repository type, change patterns, file criticality, and risk context for
                                            more accurate AI recommendations</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox"
                                                id="enhanced_prompts_fallback" name="enhanced_prompts_fallback"
                                                {{ 'checked' if config.enhanced_prompts_fallback else '' }}>
                                            <label class="form-check-label" for="enhanced_prompts_fallback">
                                                <strong>Enhanced Prompts Fallback</strong>
                                            </label>
                                        </div>
                                        <div class="form-text">Automatically fall back to basic prompts if enhanced
                                            contextual prompts fail, ensuring system reliability</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Monitoring Configuration -->
                    <div class="card mb-4">
                        <div class="card-header bg-light">
                            <h6 class="mb-0"><i class="fas fa-clock"></i> Monitoring</h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label for="check_interval" class="form-label">Check Interval (seconds)</label>
                                <input type="number" class="form-control" id="check_interval" name="check_interval"
                                    value="{{ config.check_interval }}" min="60" max="86400" required>
                                <div class="form-text">How often to check for new commits (60-86400 seconds)</div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="generate_docs"
                                            name="generate_docs" {% if config.generate_docs %}checked{% endif %}>
                                        <label class="form-check-label" for="generate_docs">
                                            Generate Documentation
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="send_emails"
                                            name="send_emails" {% if config.send_emails %}checked{% endif %}>
                                        <label class="form-check-label" for="send_emails">
                                            Send Email Notifications
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Email Configuration -->
                    <div class="card mb-4">
                        <div class="card-header bg-light">
                            <h6 class="mb-0"><i class="fas fa-envelope"></i> Email Settings</h6>
                        </div>
                        <div class="card-body" id="email-settings-body">

                            <!-- Email Provider Presets -->
                            <div class="alert alert-info mb-3">
                                <div class="d-flex align-items-center justify-content-between mb-2">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-info-circle me-2"></i>
                                        <strong>Quick Setup Options:</strong>
                                    </div>
                                    <button type="button" class="btn btn-success btn-sm" onclick="sendTestEmail()"
                                        id="testEmailBtn">
                                        <i class="fas fa-paper-plane"></i> Test Send Email
                                    </button>
                                </div>
                                <div class="btn-group" role="group" aria-label="Email provider presets">
                                    <button type="button" class="btn btn-outline-primary btn-sm"
                                        onclick="setMailHogConfig()">
                                        <i class="fas fa-bug"></i> MailHog (Development)
                                    </button>
                                    <button type="button" class="btn btn-outline-primary btn-sm"
                                        onclick="setGmailConfig()">
                                        <i class="fab fa-google"></i> Gmail
                                    </button>
                                    <button type="button" class="btn btn-outline-primary btn-sm"
                                        onclick="setOutlookConfig()">
                                        <i class="fab fa-microsoft"></i> Outlook
                                    </button>
                                    <button type="button" class="btn btn-outline-primary btn-sm"
                                        onclick="clearEmailConfig()">
                                        <i class="fas fa-times"></i> Custom
                                    </button>
                                </div>
                                <div class="mt-2">
                                    <small class="text-muted">
                                        <strong>MailHog:</strong> Development email testing (emails captured, not sent).
                                        <a href="http://localhost:8025" target="_blank" class="text-decoration-none">
                                            View MailHog Web UI <i class="fas fa-external-link-alt"></i>
                                        </a>
                                    </small>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-8">
                                    <div class="mb-3">
                                        <label for="smtp_host" class="form-label">SMTP Host</label>
                                        <input type="text" class="form-control" id="smtp_host" name="smtp_host"
                                            value="{{ config.smtp_host }}" placeholder="smtp.gmail.com">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="smtp_port" class="form-label">SMTP Port</label>
                                        <input type="number" class="form-control" id="smtp_port" name="smtp_port"
                                            value="{{ config.smtp_port }}" min="1" max="65535">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="smtp_username" class="form-label">SMTP Username</label>
                                        <input type="text" class="form-control" id="smtp_username" name="smtp_username"
                                            value="{{ config.smtp_username or '' }}" placeholder="Optional"
                                            autocomplete="username">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="smtp_password" class="form-label">SMTP Password</label>
                                        <input type="password" class="form-control" id="smtp_password"
                                            name="smtp_password" value="{{ config.smtp_password or '' }}"
                                            placeholder="Optional" autocomplete="current-password">
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="email_from" class="form-label">From Address</label>
                                <input type="email" class="form-control" id="email_from" name="email_from"
                                    value="{{ config.email_from }}" required>
                            </div>
                            <div class="mb-3">
                                <label for="email_recipients" class="form-label">Global Recipients</label>
                                <textarea class="form-control" id="email_recipients" name="email_recipients" rows="3"
                                    placeholder="<EMAIL>, <EMAIL>">{{ config.email_recipients | join(', ') }}</textarea>
                                <div class="form-text">Global email recipients who receive notifications for ALL
                                    repositories (comma-separated). Repository-specific recipients can be configured in
                                    the Repositories section.</div>
                            </div>
                        </div>
                    </div>

                    <!-- Web Interface Configuration -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5><i class="fas fa-globe"></i> Web Interface Settings</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="web_log_entries" class="form-label">Log Entries Display
                                            Count</label>
                                        <input type="number" class="form-control" id="web_log_entries"
                                            name="web_log_entries" value="{{ config.web_log_entries }}" min="50"
                                            max="1000" required>
                                        <div class="form-text">Number of recent log entries to display on the logs page
                                            (50-1000)</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle"></i>
                                        <strong>Note:</strong> Higher values may slow down the logs page loading time.
                                        Recommended range: 100-500 entries.
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Log Management Configuration -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5><i class="fas fa-file-alt"></i> Log Management Settings</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="log_cleanup_max_size_mb" class="form-label">Manual Cleanup Trigger
                                            Size (MB)</label>
                                        <input type="number" class="form-control" id="log_cleanup_max_size_mb"
                                            name="log_cleanup_max_size_mb" value="{{ config.log_cleanup_max_size_mb }}"
                                            min="10" max="500" required>
                                        <div class="form-text">Log file size that triggers manual cleanup (10-500 MB)
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="log_cleanup_lines_to_keep" class="form-label">Lines to Keep After
                                            Cleanup</label>
                                        <input type="number" class="form-control" id="log_cleanup_lines_to_keep"
                                            name="log_cleanup_lines_to_keep"
                                            value="{{ config.log_cleanup_lines_to_keep }}" min="100" max="10000"
                                            required>
                                        <div class="form-text">Number of recent log lines to preserve (100-10,000)</div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="log_rotation_max_size_mb" class="form-label">Automatic Rotation Size
                                            (MB)</label>
                                        <input type="number" class="form-control" id="log_rotation_max_size_mb"
                                            name="log_rotation_max_size_mb"
                                            value="{{ config.log_rotation_max_size_mb }}" min="5" max="100" required>
                                        <div class="form-text">Log file size that triggers automatic rotation (5-100 MB)
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="log_rotation_backup_count" class="form-label">Backup Files to
                                            Keep</label>
                                        <input type="number" class="form-control" id="log_rotation_backup_count"
                                            name="log_rotation_backup_count"
                                            value="{{ config.log_rotation_backup_count }}" min="1" max="20" required>
                                        <div class="form-text">Number of rotated backup files to retain (1-20)</div>
                                    </div>
                                </div>
                            </div>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i>
                                <strong>Log Management:</strong>
                                <ul class="mb-0 mt-2">
                                    <li><strong>Manual Cleanup:</strong> Triggered by the "Cleanup" button on the logs
                                        page</li>
                                    <li><strong>Automatic Rotation:</strong> Happens automatically when the log file
                                        grows too large</li>
                                    <li><strong>Backup Files:</strong> Rotated files are saved as .log.1, .log.2, etc.
                                    </li>
                                    <li><strong>Restart Required:</strong> Log rotation settings require container
                                        restart to take effect</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- LDAP Configuration -->
                    <div class="card mb-4">
                        <div class="card-header bg-light d-flex justify-content-between align-items-center">
                            <h6 class="mb-0"><i class="fas fa-users-cog"></i> LDAP Integration</h6>
                            <div>
                                <button type="button" class="btn btn-sm btn-outline-info me-2"
                                    onclick="testLDAPConnection(this)">
                                    <i class="fas fa-plug"></i> Test Connection
                                </button>
                                <a href="{{ url_for('ldap_sync_page') }}" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-cog"></i> Manage LDAP
                                </a>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="ldap_sync_enabled"
                                        name="ldap_sync_enabled" {% if config.ldap_sync_enabled %}checked{% endif %}>
                                    <label class="form-check-label" for="ldap_sync_enabled">
                                        <strong>Enable LDAP User Synchronization</strong>
                                    </label>
                                </div>
                                <div class="form-text">Automatically synchronize users from LDAP directory</div>
                            </div>

                            <div id="ldap-config-section" {% if not config.ldap_sync_enabled %}style="display: none;" {%
                                endif %}>
                                <!-- Connection Settings -->
                                <h6 class="text-muted mb-3"><i class="fas fa-server"></i> Connection Settings</h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="ldap_server" class="form-label">LDAP Server</label>
                                            <input type="text" class="form-control" id="ldap_server" name="ldap_server"
                                                value="{{ config.ldap_server or '' }}" placeholder="ldap.company.com">
                                            <div class="form-text">LDAP server hostname or IP address</div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label for="ldap_port" class="form-label">Port</label>
                                            <input type="number" class="form-control" id="ldap_port" name="ldap_port"
                                                value="{{ config.ldap_port or 389 }}" min="1" max="65535">
                                            <div class="form-text">389 (LDAP) or 636 (LDAPS)</div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <div class="form-check mt-4">
                                                <input class="form-check-input" type="checkbox" id="ldap_use_ssl"
                                                    name="ldap_use_ssl" {% if config.ldap_use_ssl %}checked{% endif %}>
                                                <label class="form-check-label" for="ldap_use_ssl">
                                                    Use SSL/TLS
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="ldap_bind_dn" class="form-label">Bind DN</label>
                                            <input type="text" class="form-control" id="ldap_bind_dn"
                                                name="ldap_bind_dn" value="{{ config.ldap_bind_dn or '' }}"
                                                placeholder="CN=service-account,OU=Services,DC=company,DC=com">
                                            <div class="form-text">Service account distinguished name</div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="ldap_bind_password" class="form-label">Bind Password</label>
                                            <input type="password" class="form-control" id="ldap_bind_password"
                                                name="ldap_bind_password" value="{{ config.ldap_bind_password or '' }}"
                                                placeholder="Service account password">
                                            <div class="form-text">Service account password</div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="ldap_user_base_dn" class="form-label">User Base DN</label>
                                            <input type="text" class="form-control" id="ldap_user_base_dn"
                                                name="ldap_user_base_dn" value="{{ config.ldap_user_base_dn or '' }}"
                                                placeholder="OU=Users,DC=company,DC=com">
                                            <div class="form-text">Base DN for user searches</div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="ldap_user_filter" class="form-label">User Filter</label>
                                            <input type="text" class="form-control" id="ldap_user_filter"
                                                name="ldap_user_filter"
                                                value="{{ config.ldap_user_filter or '(objectClass=person)' }}"
                                                placeholder="(objectClass=person)">
                                            <div class="form-text">LDAP filter for user objects</div>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="ldap_sync_interval" class="form-label">Sync Interval (seconds)</label>
                                    <input type="number" class="form-control" id="ldap_sync_interval"
                                        name="ldap_sync_interval" value="{{ config.ldap_sync_interval or 3600 }}"
                                        min="300" max="86400">
                                    <div class="form-text">How often to sync users (300 = 5 minutes, 3600 = 1 hour)
                                    </div>
                                </div>

                                <!-- Attribute Mapping -->
                                <h6 class="text-muted mb-3 mt-4"><i class="fas fa-tags"></i> Attribute Mapping</h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="ldap_username_attr" class="form-label">Username
                                                Attribute</label>
                                            <input type="text" class="form-control" id="ldap_username_attr"
                                                name="ldap_username_attr"
                                                value="{{ config.ldap_username_attr or 'sAMAccountName' }}"
                                                placeholder="sAMAccountName">
                                            <div class="form-text">LDAP attribute for username (sAMAccountName for AD,
                                                uid for OpenLDAP)</div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="ldap_email_attr" class="form-label">Email Attribute</label>
                                            <input type="text" class="form-control" id="ldap_email_attr"
                                                name="ldap_email_attr" value="{{ config.ldap_email_attr or 'mail' }}"
                                                placeholder="mail">
                                            <div class="form-text">LDAP attribute for email address</div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="ldap_fullname_attr" class="form-label">Full Name
                                                Attribute</label>
                                            <input type="text" class="form-control" id="ldap_fullname_attr"
                                                name="ldap_fullname_attr"
                                                value="{{ config.ldap_fullname_attr or 'displayName' }}"
                                                placeholder="displayName">
                                            <div class="form-text">LDAP attribute for full name (displayName for AD, cn
                                                for OpenLDAP)</div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="ldap_phone_attr" class="form-label">Phone Attribute</label>
                                            <input type="text" class="form-control" id="ldap_phone_attr"
                                                name="ldap_phone_attr"
                                                value="{{ config.ldap_phone_attr or 'telephoneNumber' }}"
                                                placeholder="telephoneNumber">
                                            <div class="form-text">LDAP attribute for phone number</div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="ldap_groups_attr" class="form-label">Groups Attribute</label>
                                            <input type="text" class="form-control" id="ldap_groups_attr"
                                                name="ldap_groups_attr"
                                                value="{{ config.ldap_groups_attr or 'memberOf' }}"
                                                placeholder="memberOf">
                                            <div class="form-text">LDAP attribute for group membership</div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="ldap_default_role" class="form-label">Default Role</label>
                                            <select class="form-control" id="ldap_default_role"
                                                name="ldap_default_role">
                                                <option value="VIEWER" {% if config.ldap_default_role=='VIEWER'
                                                    %}selected{% endif %}>VIEWER</option>
                                                <option value="DEVELOPER" {% if config.ldap_default_role=='DEVELOPER'
                                                    %}selected{% endif %}>DEVELOPER</option>
                                                <option value="MANAGER" {% if config.ldap_default_role=='MANAGER'
                                                    %}selected{% endif %}>MANAGER</option>
                                                <option value="ADMIN" {% if config.ldap_default_role=='ADMIN'
                                                    %}selected{% endif %}>ADMIN</option>
                                            </select>
                                            <div class="form-text">Default role for users with no matching groups</div>
                                        </div>
                                    </div>
                                </div>

                                <div class="alert alert-info mt-3">
                                    <i class="fas fa-info-circle"></i>
                                    <strong>Group-to-Role Mapping:</strong> Configure group mappings in the
                                    <a href="{{ url_for('ldap_sync_page') }}" class="alert-link">LDAP Management</a>
                                    page.
                                    Changes to connection settings require a service restart.
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- SQL Database Integration Configuration -->
                    <div class="card mb-4">
                        <div class="card-header bg-light d-flex justify-content-between align-items-center">
                            <h6 class="mb-0"><i class="fas fa-database"></i> SQL Database Integration</h6>
                            <div>
                                <button type="button" class="btn btn-sm btn-outline-info me-2"
                                    onclick="testSQLConnection(this)">
                                    <i class="fas fa-plug"></i> Test Connection
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-secondary"
                                    onclick="showSQLQueryEditor()">
                                    <i class="fas fa-edit"></i> Edit Query
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="sql_enabled" name="sql_enabled"
                                        {% if config.sql_config.enabled %}checked{% endif %}>
                                    <label class="form-check-label" for="sql_enabled">
                                        <strong>Enable Change Request Integration</strong>
                                    </label>
                                </div>
                                <div class="form-text">Integrate with SQL database to retrieve change request
                                    information for commit analysis</div>
                            </div>

                            <div id="sql-config-section" {% if not config.sql_config.enabled %}style="display: none;" {%
                                endif %}>
                                <!-- Connection Settings -->
                                <h6 class="text-muted mb-3"><i class="fas fa-server"></i> Database Connection</h6>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="sql_driver" class="form-label">Database Type</label>
                                            <select class="form-control" id="sql_driver" name="sql_driver">
                                                <option value="mysql" {% if config.sql_config.driver=='mysql'
                                                    %}selected{% endif %}>MySQL</option>
                                                <option value="postgresql" {% if config.sql_config.driver=='postgresql'
                                                    %}selected{% endif %}>PostgreSQL</option>
                                                <option value="sqlite" {% if config.sql_config.driver=='sqlite'
                                                    %}selected{% endif %}>SQLite</option>
                                                <option value="mssql" {% if config.sql_config.driver=='mssql'
                                                    %}selected{% endif %}>SQL Server</option>
                                            </select>
                                            <div class="form-text">Database type for change request storage</div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="sql_host" class="form-label">Host</label>
                                            <input type="text" class="form-control" id="sql_host" name="sql_host"
                                                value="{{ config.sql_config.host or 'mysql-test' }}"
                                                placeholder="mysql-test">
                                            <div class="form-text">Database server hostname</div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="sql_port" class="form-label">Port</label>
                                            <input type="number" class="form-control" id="sql_port" name="sql_port"
                                                value="{{ config.sql_config.port or 3306 }}" min="1" max="65535">
                                            <div class="form-text">Database server port</div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="sql_database" class="form-label">Database Name</label>
                                            <input type="text" class="form-control" id="sql_database"
                                                name="sql_database" value="{{ config.sql_config.database or '' }}"
                                                placeholder="change_requests">
                                            <div class="form-text">Name of the database</div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="sql_username" class="form-label">Username</label>
                                            <input type="text" class="form-control" id="sql_username"
                                                name="sql_username" value="{{ config.sql_config.username or '' }}"
                                                placeholder="reposense">
                                            <div class="form-text">Database username</div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="sql_password" class="form-label">Password</label>
                                            <input type="password" class="form-control" id="sql_password"
                                                name="sql_password" value="{{ config.sql_config.password or '' }}"
                                                placeholder="Password">
                                            <div class="form-text">Database password</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Query Configuration -->
                                <h6 class="text-muted mb-3 mt-4"><i class="fas fa-search"></i> Change Request Query</h6>
                                <div class="mb-3">
                                    <label for="sql_query" class="form-label">SQL Query Template</label>
                                    <textarea class="form-control font-monospace" id="sql_query" name="sql_query"
                                        rows="4"
                                        placeholder="SELECT id, number, title, description, priority, status, created_date, assigned_to, category, risk_level FROM change_requests WHERE number = :change_request_number">{{ config.sql_config.change_request_query or '' }}</textarea>
                                    <div class="form-text">
                                        <strong>Important:</strong> Use column aliases to map your database fields to
                                        expected names:
                                        <code>SELECT [YOUR_ID_FIELD] as id, [YOUR_NUMBER_FIELD] as number, [YOUR_TITLE_FIELD] as title, ...</code>
                                        <br>Use <code>:change_request_number</code> as parameter placeholder.
                                        <br><a href="/docs/FIELD_MAPPING_REFERENCE.md" target="_blank"
                                            class="text-primary">📖 View Field Mapping Reference Guide</a>
                                    </div>
                                </div>

                                <!-- Pattern Configuration -->
                                <h6 class="text-muted mb-3 mt-4"><i class="fas fa-regex"></i> Extraction Patterns</h6>
                                <div class="mb-3">
                                    <label for="sql_patterns" class="form-label">Change Request Patterns</label>
                                    <textarea class="form-control font-monospace" id="sql_patterns" name="sql_patterns"
                                        rows="5"
                                        placeholder="BUG[#\-]\s*(\d+)&#10;CR[#\-]\s*(\d+)&#10;CR\s+Number:\s*(\d+)&#10;Issue[#\-]\s*(\d+)&#10;#(\d+)"
                                        required>{{ config.sql_config.change_request_patterns | join('&#10;') if config.sql_config.change_request_patterns else '' }}</textarea>
                                    <div class="form-text">Regular expression patterns to extract change request numbers
                                        from commit messages (one per line). <strong>Required:</strong> These patterns
                                        are used throughout the system.</div>
                                </div>

                                <!-- Field Mapping Configuration -->
                                <h6 class="text-muted mb-3 mt-4"><i class="fas fa-map"></i> Field Mappings</h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="risk_level_field" class="form-label">Risk Level Field</label>
                                            <input type="text" class="form-control" id="risk_level_field"
                                                name="risk_level_field"
                                                value="{{ config.sql_config.field_mappings.get('risk_level_field', 'risk_level') }}"
                                                placeholder="risk_level">
                                            <div class="form-text">Database field name for explicit risk level</div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="priority_field" class="form-label">Priority Field</label>
                                            <input type="text" class="form-control" id="priority_field"
                                                name="priority_field"
                                                value="{{ config.sql_config.field_mappings.get('priority_field', 'priority') }}"
                                                placeholder="priority">
                                            <div class="form-text">Database field name for priority information</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="category_field" class="form-label">Category Field</label>
                                            <input type="text" class="form-control" id="category_field"
                                                name="category_field"
                                                value="{{ config.sql_config.field_mappings.get('category_field', 'category') }}"
                                                placeholder="category">
                                            <div class="form-text">Database field name for category/type information
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="status_field" class="form-label">Status Field</label>
                                            <input type="text" class="form-control" id="status_field"
                                                name="status_field"
                                                value="{{ config.sql_config.field_mappings.get('status_field', 'status') }}"
                                                placeholder="status">
                                            <div class="form-text">Database field name for status information</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Value Mapping Configuration -->
                                <h6 class="text-muted mb-3 mt-4"><i class="fas fa-exchange-alt"></i> Value Mappings</h6>
                                <div class="mb-3">
                                    <label for="priority_mappings" class="form-label">Priority to Risk Mappings</label>
                                    <textarea class="form-control font-monospace" id="priority_mappings"
                                        name="priority_mappings" rows="4"
                                        placeholder="CRITICAL=CRITICAL&#10;URGENT=CRITICAL&#10;HIGH=HIGH&#10;IMPORTANT=HIGH&#10;MEDIUM=MEDIUM&#10;NORMAL=MEDIUM&#10;LOW=LOW&#10;MINOR=LOW">{% for key, value in config.sql_config.value_mappings.get('priority_to_risk', {}).items() %}{{ key }}={{ value }}{% if not loop.last %}&#10;{% endif %}{% endfor %}</textarea>
                                    <div class="form-text">Map priority values to risk levels (format:
                                        PRIORITY_VALUE=RISK_LEVEL, one per line)</div>
                                </div>
                                <div class="mb-3">
                                    <label for="category_mappings" class="form-label">Category Risk Boost
                                        Mappings</label>
                                    <textarea class="form-control font-monospace" id="category_mappings"
                                        name="category_mappings" rows="3"
                                        placeholder="SECURITY=CRITICAL&#10;CRITICAL_BUG=CRITICAL&#10;DATA_LOSS=CRITICAL&#10;PERFORMANCE=HIGH&#10;INTEGRATION=HIGH&#10;API_CHANGE=HIGH">{% for key, value in config.sql_config.value_mappings.get('category_risk_boost', {}).items() %}{{ key }}={{ value }}{% if not loop.last %}&#10;{% endif %}{% endfor %}</textarea>
                                    <div class="form-text">Categories that boost risk level (format:
                                        CATEGORY=RISK_LEVEL, one per line)</div>
                                </div>

                                <!-- Risk Analysis Configuration -->
                                <h6 class="text-muted mb-3 mt-4"><i class="fas fa-cogs"></i> Risk Analysis Settings</h6>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="default_risk_level" class="form-label">Default Risk
                                                Level</label>
                                            <select class="form-control" id="default_risk_level"
                                                name="default_risk_level">
                                                <option value="LOW" {% if
                                                    config.sql_config.risk_analysis_config.get('default_risk_level')=='LOW'
                                                    %}selected{% endif %}>LOW</option>
                                                <option value="MEDIUM" {% if
                                                    config.sql_config.risk_analysis_config.get('default_risk_level')=='MEDIUM'
                                                    %}selected{% endif %}>MEDIUM</option>
                                                <option value="HIGH" {% if
                                                    config.sql_config.risk_analysis_config.get('default_risk_level')=='HIGH'
                                                    %}selected{% endif %}>HIGH</option>
                                                <option value="CRITICAL" {% if
                                                    config.sql_config.risk_analysis_config.get('default_risk_level')=='CRITICAL'
                                                    %}selected{% endif %}>CRITICAL</option>
                                            </select>
                                            <div class="form-text">Default risk level for unknown priorities</div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="confidence_threshold" class="form-label">Confidence
                                                Threshold</label>
                                            <input type="number" class="form-control" id="confidence_threshold"
                                                name="confidence_threshold"
                                                value="{{ config.sql_config.risk_analysis_config.get('confidence_threshold', 0.3) }}"
                                                min="0.1" max="1.0" step="0.1">
                                            <div class="form-text">Minimum confidence to return risk level (0.1-1.0)
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="category_weight" class="form-label">Category Weight</label>
                                            <input type="number" class="form-control" id="category_weight"
                                                name="category_weight"
                                                value="{{ config.sql_config.risk_analysis_config.get('category_weight', 0.3) }}"
                                                min="0.1" max="1.0" step="0.1">
                                            <div class="form-text">Weight for category-based risk boosts (0.1-1.0)</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Change Request Summary Display Configuration -->
                                <h6 class="text-muted mb-3 mt-4"><i class="fas fa-file-alt"></i> Document Summary
                                    Settings</h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="summary_enabled"
                                                    name="summary_enabled" {% if
                                                    config.sql_config.summary_display_config.get('enabled', True)
                                                    %}checked{% endif %}>
                                                <label class="form-check-label" for="summary_enabled">
                                                    Include Change Request Summary in Documents
                                                </label>
                                            </div>
                                            <div class="form-text">Add a formatted summary section to revision documents
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="summary_section_title" class="form-label">Summary Section
                                                Title</label>
                                            <input type="text" class="form-control" id="summary_section_title"
                                                name="summary_section_title"
                                                value="{{ config.sql_config.summary_display_config.get('section_title', 'Change Request Summary') }}"
                                                placeholder="Change Request Summary">
                                            <div class="form-text">Title for the change request summary section</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="description_max_length" class="form-label">Description Max
                                                Length</label>
                                            <input type="number" class="form-control" id="description_max_length"
                                                name="description_max_length"
                                                value="{{ config.sql_config.summary_display_config.get('description_max_length', 300) }}"
                                                min="50" max="1000">
                                            <div class="form-text">Maximum characters for description field</div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="date_format" class="form-label">Date Format</label>
                                            <select class="form-control" id="date_format" name="date_format">
                                                <option value="%Y-%m-%d" {% if
                                                    config.sql_config.summary_display_config.get('date_format')=='%Y-%m-%d'
                                                    %}selected{% endif %}>YYYY-MM-DD</option>
                                                <option value="%m/%d/%Y" {% if
                                                    config.sql_config.summary_display_config.get('date_format')=='%m/%d/%Y'
                                                    %}selected{% endif %}>MM/DD/YYYY</option>
                                                <option value="%d/%m/%Y" {% if
                                                    config.sql_config.summary_display_config.get('date_format')=='%d/%m/%Y'
                                                    %}selected{% endif %}>DD/MM/YYYY</option>
                                                <option value="%B %d, %Y" {% if
                                                    config.sql_config.summary_display_config.get('date_format')=='%B %d, %Y'
                                                    %}selected{% endif %}>Month DD, YYYY</option>
                                            </select>
                                            <div class="form-text">Format for displaying dates</div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <div class="form-check mt-4">
                                                <input class="form-check-input" type="checkbox" id="show_empty_fields"
                                                    name="show_empty_fields" {% if
                                                    config.sql_config.summary_display_config.get('show_empty_fields',
                                                    False) %}checked{% endif %}>
                                                <label class="form-check-label" for="show_empty_fields">
                                                    Show Empty Fields
                                                </label>
                                            </div>
                                            <div class="form-text">Display fields even when they have no value</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="display_fields" class="form-label">Display Fields (in order)</label>
                                    <textarea class="form-control font-monospace" id="display_fields"
                                        name="display_fields" rows="3"
                                        placeholder="number&#10;title&#10;priority&#10;status&#10;risk_level&#10;category&#10;assigned_to&#10;assigned_to_tester&#10;created_date&#10;description">{{ config.sql_config.summary_display_config.get('display_fields', []) | join('&#10;') }}</textarea>
                                    <div class="form-text">Fields to display in summary (one per line, in display order)
                                    </div>
                                </div>

                                <div class="alert alert-info mt-3">
                                    <i class="fas fa-info-circle"></i>
                                    <strong>Test Setup:</strong> Use the test MySQL database at
                                    <code>mysql-test:3306</code>
                                    with database <code>change_requests</code>, username <code>reposense</code>,
                                    password <code>reposense123</code>.
                                    <a href="http://localhost:8080" target="_blank" class="alert-link">Access
                                        Adminer</a> to manage test data.
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Enhanced System Reset -->
                    <div class="card mb-4">
                        <div class="card-header bg-light">
                            <h6 class="mb-0"><i class="fas fa-undo-alt"></i> System Reset & Management</h6>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-danger" role="alert">
                                <i class="fas fa-exclamation-triangle"></i>
                                <strong>Warning:</strong> Reset operations are irreversible. Comprehensive backups are
                                created automatically before any reset.
                            </div>

                            <!-- Quick Reset Options -->
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <div class="border rounded p-3 bg-light">
                                        <h6 class="text-primary"><i class="fas fa-database me-2"></i>Quick Database
                                            Reset</h6>
                                        <p class="text-muted small mb-3">
                                            Reset only the database and documents. All configuration, repositories, and
                                            users remain unchanged.
                                        </p>
                                        <button type="button" class="btn btn-warning" onclick="resetDatabase()">
                                            <i class="fas fa-database"></i> Reset Database Only
                                        </button>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="border rounded p-3 bg-light">
                                        <h6 class="text-danger"><i class="fas fa-broom me-2"></i>Complete System Reset
                                        </h6>
                                        <p class="text-muted small mb-3">
                                            Reset everything to factory defaults. Use when you want a completely fresh
                                            start.
                                        </p>
                                        <button type="button" class="btn btn-danger" onclick="showCompleteResetModal()">
                                            <i class="fas fa-undo-alt"></i> Complete Reset
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- System Information -->
                            <div class="row">
                                <div class="col-12">
                                    <div class="border rounded p-3">
                                        <h6 class="text-muted"><i class="fas fa-info-circle me-2"></i>System Information
                                        </h6>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <small class="text-muted">
                                                    <strong>Database:</strong> <code>/app/data/documents.db</code><br>
                                                    <strong>Configuration:</strong>
                                                    <code>/app/data/config.json</code><br>
                                                    <strong>Documents:</strong>
                                                    <code>/app/data/output/repositories/</code>
                                                </small>
                                            </div>
                                            <div class="col-md-6">
                                                <small class="text-muted">
                                                    <strong>Current Status:</strong><br>
                                                    • Repositories: {{ config.repositories|length }}<br>
                                                    • Users: {{ config.users|length }}<br>
                                                    • Email Recipients: {{ config.email_recipients|length }}
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('index') }}" class="btn btn-secondary me-md-2">Cancel</a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Save Configuration
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Enhanced Reset Modal -->
<div class="modal fade" id="enhancedResetModal" tabindex="-1" aria-labelledby="enhancedResetModalLabel"
    aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="enhancedResetModalLabel">
                    <i class="fas fa-undo-alt me-2"></i>Enhanced System Reset
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"
                    aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Choose what to reset:</strong> Select only the components you want to reset. Automatic
                    backups will be created for critical data.
                </div>

                <!-- Backup Information -->
                <div class="alert alert-info" role="alert">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>What Gets Backed Up:</strong>
                    <ul class="mb-0 mt-2">
                        <li><strong>config.json</strong> - All configuration (repositories, users, email, AI settings)
                        </li>
                        <li><strong>documents.db</strong> - Document database and analysis data</li>
                        <li><strong>Repository files</strong> - Local repository checkouts (if database reset selected)
                        </li>
                        <li><strong>Generated documents</strong> - AI-generated revision documents (if database reset
                            selected)</li>
                        <li><strong>System logs</strong> - Current system activity log</li>
                    </ul>
                    <small class="text-muted mt-2 d-block">
                        <i class="fas fa-clock"></i> Backups are timestamped: <code>filename.backup.{timestamp}</code>
                    </small>
                </div>

                <!-- Reset Options -->
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="fas fa-cogs me-2"></i>Reset Options</h6>

                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="reset_database"
                                onchange="updateResetPreview(); validateResetOptions();">
                            <label class="form-check-label" for="reset_database">
                                <i class="fas fa-database me-1"></i> Database and Documents
                            </label>
                            <small class="text-muted d-block">Clear all document analysis and scan history</small>
                        </div>

                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="reset_repositories"
                                onchange="updateResetPreview(); validateResetOptions();">
                            <label class="form-check-label" for="reset_repositories">
                                <i class="fas fa-code-branch me-1"></i> Repository Configuration
                            </label>
                            <small class="text-muted d-block">Remove all repository settings and credentials</small>
                        </div>

                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="reset_users"
                                onchange="updateResetPreview(); validateResetOptions();">
                            <label class="form-check-label" for="reset_users">
                                <i class="fas fa-users me-1"></i> User Accounts
                            </label>
                            <small class="text-muted d-block">Remove all user accounts and permissions</small>
                        </div>

                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="reset_email_config"
                                onchange="updateResetPreview(); validateResetOptions();">
                            <label class="form-check-label" for="reset_email_config">
                                <i class="fas fa-envelope me-1"></i> Email Configuration
                            </label>
                            <small class="text-muted d-block">Reset email settings and recipient lists</small>
                        </div>

                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="reset_svn_config"
                                onchange="updateResetPreview(); validateResetOptions();">
                            <label class="form-check-label" for="reset_svn_config">
                                <i class="fas fa-server me-1"></i> SVN/Git Server Settings
                            </label>
                            <small class="text-muted d-block">Reset server URLs and credentials</small>
                        </div>

                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="reset_ai_config"
                                onchange="updateResetPreview(); validateResetOptions();">
                            <label class="form-check-label" for="reset_ai_config">
                                <i class="fas fa-robot me-1"></i> AI Model Configuration
                            </label>
                            <small class="text-muted d-block">Reset AI model settings and preferences</small>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <h6><i class="fas fa-eye me-2"></i>Reset Preview</h6>
                        <div id="resetPreview" class="border rounded p-3 bg-light">
                            <p class="text-muted mb-0">Select reset options above to see what will be reset...</p>
                        </div>

                        <div class="mt-3">
                            <button type="button" class="btn btn-outline-primary btn-sm me-2"
                                onclick="selectAllReset()">
                                <i class="fas fa-check-square"></i> Select All
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="selectNoneReset()">
                                <i class="fas fa-square"></i> Select None
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Safety Validation -->
                <div class="mt-3">
                    <div class="alert alert-info" role="alert" id="safetyValidation" style="display: none;">
                        <i class="fas fa-shield-alt me-2"></i>
                        <strong>Safety Check:</strong> <span id="safetyMessage"></span>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times"></i> Cancel
                </button>
                <button type="button" class="btn btn-danger" id="executeResetBtn" onclick="executeEnhancedReset()"
                    disabled>
                    <i class="fas fa-undo-alt"></i> Execute Reset
                </button>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block scripts %}
<script>
    // Load available Ollama models
    function loadOllamaModels(customHost = null) {
        const modelSelect = document.getElementById('ollama_model');
        const refreshBtn = document.getElementById('refresh-models');
        const statusText = document.getElementById('model-status');
        const currentValue = '{{ config.ollama_model }}';
        const ollamaHostInput = document.getElementById('ollama_host');

        // Show loading state
        refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        refreshBtn.disabled = true;
        statusText.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading models from Ollama server...';

        // Determine which host to use
        const hostToTest = customHost || (ollamaHostInput ? ollamaHostInput.value.trim() : null);
        const currentConfigHost = '{{ config.ollama_host }}'.trim();

        console.log('loadOllamaModels debug:', {
            customHost,
            hostToTest,
            currentConfigHost,
            willUseTestEndpoint: hostToTest && hostToTest !== currentConfigHost && customHost
        });

        let fetchPromise;
        if (hostToTest && hostToTest !== currentConfigHost && customHost) {
            // Only use test-models endpoint when explicitly testing a custom host
            console.log('Using /api/ollama/test-models for host:', hostToTest);
            fetchPromise = fetch('/api/ollama/test-models', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    ollama_host: hostToTest
                })
            });
        } else {
            // Use current configuration for normal loads and when host matches
            console.log('Using /api/ollama/models');
            fetchPromise = fetch('/api/ollama/models');
        }

        fetchPromise
            .then(response => response.json())
            .then(data => {
                // Clear existing options
                modelSelect.innerHTML = '';

                if (data.connected && data.models.length > 0) {
                    // Add available models
                    data.models.forEach(model => {
                        const option = document.createElement('option');
                        option.value = model;
                        option.textContent = model;
                        if (model === currentValue) {
                            option.selected = true;
                        }
                        modelSelect.appendChild(option);
                    });

                    // If current model is not in the list, add it as the first option
                    if (currentValue && !data.models.includes(currentValue)) {
                        const option = document.createElement('option');
                        option.value = currentValue;
                        option.textContent = currentValue + ' (not found)';
                        option.selected = true;
                        modelSelect.insertBefore(option, modelSelect.firstChild);
                    }

                    const hostInfo = data.host ? ` from ${data.host}` : '';
                    statusText.innerHTML = `<i class="fas fa-check text-success"></i> Found ${data.models.length} available models${hostInfo}`;

                    // Also populate specialized model dropdowns
                    populateSpecializedModels(data.models);
                } else if (!data.connected) {
                    // Ollama not connected
                    const option = document.createElement('option');
                    option.value = currentValue || 'qwen3';
                    option.textContent = (currentValue || 'qwen3') + ' (Ollama not connected)';
                    option.selected = true;
                    modelSelect.appendChild(option);

                    statusText.innerHTML = '<i class="fas fa-exclamation-triangle text-warning"></i> Ollama server not connected';
                } else {
                    // No models available
                    const option = document.createElement('option');
                    option.value = currentValue || '';
                    option.textContent = 'No models available';
                    modelSelect.appendChild(option);

                    statusText.innerHTML = '<i class="fas fa-info-circle text-info"></i> No models found on Ollama server';
                }
            })
            .catch(error => {
                console.error('Error loading models:', error);
                // Fallback to server-side models or current model
                const serverModels = {{ available_models | tojson
            }};
    modelSelect.innerHTML = '';

    if (serverModels && serverModels.length > 0) {
        serverModels.forEach(model => {
            const option = document.createElement('option');
            option.value = model;
            option.textContent = model;
            if (model === currentValue) {
                option.selected = true;
            }
            modelSelect.appendChild(option);
        });
        statusText.innerHTML = `<i class="fas fa-check text-success"></i> Using cached models (${serverModels.length} available)`;
    } else {
        // Final fallback to current model
        const option = document.createElement('option');
        option.value = currentValue || 'qwen3';
        option.textContent = (currentValue || 'qwen3') + ' (error loading models)';
        option.selected = true;
        modelSelect.appendChild(option);
        statusText.innerHTML = '<i class="fas fa-exclamation-circle text-danger"></i> Error loading models from server';
    }
            })
            .finally(() => {
        // Reset refresh button
        refreshBtn.innerHTML = '<i class="fas fa-sync-alt"></i>';
        refreshBtn.disabled = false;
    });
}

    // Load models on page load
    document.addEventListener('DOMContentLoaded', function () {
        loadOllamaModels();
    });

    // Refresh models button
    document.getElementById('refresh-models').addEventListener('click', function () {
        loadOllamaModels();
    });

    // Auto-refresh models when Ollama host changes
    document.getElementById('ollama_host').addEventListener('blur', function () {
        const newHost = this.value.trim();
        const currentHost = '{{ config.ollama_host }}'.trim();

        console.log('Host change check:', { newHost, currentHost, different: newHost !== currentHost });

        // Only test if the host has actually changed and is not empty
        if (newHost && newHost !== currentHost) {
            console.log('Ollama host changed, testing new host:', newHost);
            loadOllamaModels(newHost);
        } else if (newHost === currentHost) {
            console.log('Host is same as current, using normal load');
            loadOllamaModels(); // Call without parameters to use normal endpoint
        }
    });

    // Populate specialized model dropdowns
    function populateSpecializedModels(models) {
        const specializedSelects = document.querySelectorAll('.specialized-model-select');
        const currentValues = {
            'ollama_model_documentation': '{{ config.ollama_model_documentation or "" }}',
            'ollama_model_code_review': '{{ config.ollama_model_code_review or "" }}',
            'ollama_model_risk_assessment': '{{ config.ollama_model_risk_assessment or "" }}'
        };

        specializedSelects.forEach(select => {
            const currentValue = currentValues[select.id];

            // Clear existing options except the first one (Use default model)
            while (select.children.length > 1) {
                select.removeChild(select.lastChild);
            }

            // Add available models
            models.forEach(model => {
                const option = document.createElement('option');
                option.value = model;
                option.textContent = model;
                if (model === currentValue) {
                    option.selected = true;
                }
                select.appendChild(option);
            });

            // If current model is not in the list but is set, add it
            if (currentValue && !models.includes(currentValue)) {
                const option = document.createElement('option');
                option.value = currentValue;
                option.textContent = currentValue + ' (not found)';
                option.selected = true;
                select.appendChild(option);
            }
        });
    }

    // Form validation
    document.querySelector('form').addEventListener('submit', function (e) {
        const checkInterval = parseInt(document.getElementById('check_interval').value);

        if (checkInterval < 60 || checkInterval > 86400) {
            alert('Check interval must be between 60 and 86400 seconds');
            e.preventDefault();
            return;
        }
    });

    // Show/hide email settings based on checkbox
    function toggleEmailSettings() {
        const sendEmailsCheckbox = document.getElementById('send_emails');
        const emailSettings = document.getElementById('email-settings-body');

        if (!sendEmailsCheckbox || !emailSettings) {
            console.error('Email settings elements not found');
            return;
        }

        try {
            if (sendEmailsCheckbox.checked) {
                emailSettings.style.opacity = '1';
                emailSettings.style.pointerEvents = 'auto';
                emailSettings.querySelectorAll('input, textarea').forEach(el => {
                    el.disabled = false;
                    el.style.cursor = 'auto';
                });
            } else {
                emailSettings.style.opacity = '0.5';
                emailSettings.style.pointerEvents = 'none';
                emailSettings.querySelectorAll('input, textarea').forEach(el => {
                    el.disabled = true;
                    el.style.cursor = 'not-allowed';
                });
            }
        } catch (error) {
            console.error('Error toggling email settings:', error);
        }
    }

    document.getElementById('send_emails').addEventListener('change', toggleEmailSettings);

    // Test Ollama Connection function
    async function testOllamaConnection(button) {
        const originalText = button.innerHTML;

        // Show loading state
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Testing...';
        button.disabled = true;

        try {
            const response = await fetch('/api/test_ollama');
            const result = await response.json();

            if (result.connected) {
                button.innerHTML = '<i class="fas fa-check text-success"></i> Connected!';
                button.className = 'btn btn-outline-success';

                // Show success message
                showAlert('Ollama connection successful!', 'success', 'alert-ollama-test');
            } else {
                button.innerHTML = '<i class="fas fa-times text-danger"></i> Failed';
                button.className = 'btn btn-outline-danger';

                // Show error message
                showAlert('Failed to connect to Ollama server. Please check the host URL and ensure Ollama is running.', 'danger', 'alert-ollama-test');
            }
        } catch (error) {
            console.error('Error testing Ollama connection:', error);
            button.innerHTML = '<i class="fas fa-times text-danger"></i> Error';
            button.className = 'btn btn-outline-danger';

            // Show error message
            showAlert('Error testing Ollama connection: ' + error.message, 'danger', 'alert-ollama-test');
        }

        // Reset button after 3 seconds
        setTimeout(() => {
            button.innerHTML = originalText;
            button.className = 'btn btn-outline-secondary';
            button.disabled = false;
        }, 3000);
    }

    // Helper function to show alerts (unified version)
    function showAlert(message, type, alertClass = 'alert-general') {
        // Remove existing alerts of the same class
        const existingAlerts = document.querySelectorAll(`.${alertClass}`);
        existingAlerts.forEach(alert => alert.remove());

        // Create new alert
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show ${alertClass} mt-3`;
        alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

        // Find the best place to insert the alert based on alert class
        let insertTarget = null;

        // For LDAP alerts, insert after LDAP section header
        if (alertClass.includes('ldap')) {
            const ldapHeader = document.querySelector('.card-header h6 i.fa-users-cog');
            if (ldapHeader) {
                insertTarget = ldapHeader.closest('.card').querySelector('.card-body');
                if (insertTarget) {
                    insertTarget.insertBefore(alertDiv, insertTarget.firstChild);
                    // Scroll to the alert
                    alertDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    return;
                }
            }
        }
        // For Ollama alerts, insert after Ollama section header
        else if (alertClass.includes('ollama')) {
            const ollamaHeader = document.querySelector('.card-header h6 i.fa-robot');
            if (ollamaHeader) {
                insertTarget = ollamaHeader.closest('.card').querySelector('.card-body');
                if (insertTarget) {
                    insertTarget.insertBefore(alertDiv, insertTarget.firstChild);
                    // Scroll to the alert
                    alertDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    return;
                }
            }
        }

        // Default: insert at top of page header
        const header = document.querySelector('.page-header');
        if (header && header.parentNode) {
            header.parentNode.insertBefore(alertDiv, header.nextSibling);
        } else {
            // Fallback: insert at top of main content
            const container = document.querySelector('.container-fluid') || document.body;
            container.insertBefore(alertDiv, container.firstChild);
        }

        // Auto-dismiss after appropriate time based on type
        const dismissTime = type === 'success' ? 5000 : (type === 'danger' ? 8000 : 6000);
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, dismissTime);
    }

    // LDAP Configuration Functions
    async function testLDAPConnection(button) {
        const originalText = button.innerHTML;

        // Show loading state
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Testing...';
        button.disabled = true;

        try {
            // Get current form values for testing
            const formData = {
                ldap_server: document.getElementById('ldap_server')?.value || 'openldap',
                ldap_port: parseInt(document.getElementById('ldap_port')?.value || '1389'),
                ldap_use_ssl: document.getElementById('ldap_use_ssl')?.checked || false,
                ldap_bind_dn: document.getElementById('ldap_bind_dn')?.value || '',
                ldap_bind_password: document.getElementById('ldap_bind_password')?.value || '',
                ldap_user_base_dn: document.getElementById('ldap_user_base_dn')?.value || ''
            };

            const response = await fetch('/api/ldap/test-connection', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(formData)
            });

            const data = await response.json();

            if (data.success) {
                showAlert(`<strong>LDAP Connection Successful!</strong><br>${data.message}`, 'success', 'alert-ldap-test');
            } else {
                showAlert(`<strong>LDAP Connection Failed!</strong><br>${data.message}`, 'danger', 'alert-ldap-test');
            }
        } catch (error) {
            showAlert(`<strong>Connection Test Error!</strong><br>Failed to test LDAP connection: ${error.message}`, 'danger', 'alert-ldap-test');
        } finally {
            // Restore button state
            button.innerHTML = originalText;
            button.disabled = false;
        }
    }

    function toggleLDAPConfig() {
        const enabled = document.getElementById('ldap_sync_enabled').checked;
        const configSection = document.getElementById('ldap-config-section');

        if (enabled) {
            configSection.style.display = 'block';
        } else {
            configSection.style.display = 'none';
        }
    }

    // Initialize LDAP settings visibility on page load
    document.addEventListener('DOMContentLoaded', function () {
        toggleEmailSettings();

        // Add LDAP toggle listener
        const ldapEnabledCheckbox = document.getElementById('ldap_sync_enabled');
        if (ldapEnabledCheckbox) {
            ldapEnabledCheckbox.addEventListener('change', toggleLDAPConfig);
        }
    });

    // Email Provider Preset Functions
    function setMailHogConfig() {
        document.getElementById('smtp_host').value = 'mailhog';
        document.getElementById('smtp_port').value = '1025';
        document.getElementById('smtp_username').value = '';
        document.getElementById('smtp_password').value = '';
        document.getElementById('email_from').value = 'reposense-ai@localhost';

        // Auto-populate global recipients with admin and manager emails
        document.getElementById('email_recipients').value = '<EMAIL>, <EMAIL>';

        showAlert('MailHog configuration applied with admin and manager recipients. This is for development - emails will be captured but not sent. View them at <a href="http://localhost:8025" target="_blank">http://localhost:8025</a>', 'info', 'alert-email-config');

        // Create admin and manager user accounts
        createDefaultUsers();
    }

    function setGmailConfig() {
        document.getElementById('smtp_host').value = 'smtp.gmail.com';
        document.getElementById('smtp_port').value = '587';
        document.getElementById('smtp_username').value = '';
        document.getElementById('smtp_password').value = '';
        document.getElementById('email_from').value = '';

        showAlert('Gmail configuration applied. You need to:<br>1. Enable 2FA on your Gmail account<br>2. Generate an App Password<br>3. Enter your Gmail address and App Password above', 'warning', 'alert-email-config');
    }

    function setOutlookConfig() {
        document.getElementById('smtp_host').value = 'smtp-mail.outlook.com';
        document.getElementById('smtp_port').value = '587';
        document.getElementById('smtp_username').value = '';
        document.getElementById('smtp_password').value = '';
        document.getElementById('email_from').value = '';

        showAlert('Outlook configuration applied. Enter your Outlook email and password above.', 'info', 'alert-email-config');
    }

    function clearEmailConfig() {
        document.getElementById('smtp_host').value = '';
        document.getElementById('smtp_port').value = '587';
        document.getElementById('smtp_username').value = '';
        document.getElementById('smtp_password').value = '';
        document.getElementById('email_from').value = '';

        showAlert('Email configuration cleared. Enter your custom SMTP settings.', 'info', 'alert-email-config');
    }

    // Create default admin and manager users for MailHog testing
    function createDefaultUsers() {
        const defaultUsers = [
            {
                username: 'admin',
                email: '<EMAIL>',
                full_name: 'System Administrator',
                role: 'admin',
                receive_all_notifications: 'on'  // Form expects 'on' for checkboxes
            },
            {
                username: 'manager',
                email: '<EMAIL>',
                full_name: 'Project Manager',
                role: 'manager',
                receive_all_notifications: 'on'  // Form expects 'on' for checkboxes
            }
        ];

        defaultUsers.forEach(userData => {
            fetch('/api/users/add', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams(userData)
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        console.log(`✅ Created user: ${userData.username} (${userData.email})`);
                    } else {
                        console.log(`ℹ️ User ${userData.username}: ${data.message}`);
                    }
                })
                .catch(error => {
                    console.log(`⚠️ Error creating user ${userData.username}:`, error);
                });
        });
    }

    // Send test email function
    function sendTestEmail() {
        const button = document.getElementById('testEmailBtn');
        const originalText = button.innerHTML;

        // Disable button and show loading state
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Sending...';
        button.disabled = true;

        // Send test email request
        fetch('/api/test_email', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert(`✅ Test email sent successfully!<br>
                <strong>Recipients:</strong> ${data.recipients.length} recipient(s)<br>
                <strong>SMTP Server:</strong> ${data.smtp_host}:${data.smtp_port}<br>
                <strong>Recipients:</strong> ${data.recipients.join(', ')}<br>
                <small class="text-muted">Check your email client or MailHog at <a href="http://localhost:8025" target="_blank">http://localhost:8025</a></small>`, 'success', 'alert-email-test');
                } else {
                    let errorMessage = `❌ Test email failed: ${data.error}`;

                    // Provide specific guidance based on error type
                    if (data.type === 'smtp_error') {
                        errorMessage += '<br><br><strong>SMTP Troubleshooting:</strong><br>';
                        errorMessage += '• Check SMTP host and port settings<br>';
                        errorMessage += '• Verify username/password if authentication is required<br>';
                        errorMessage += '• Ensure SMTP server is accessible<br>';
                        errorMessage += '• For MailHog: Make sure it\'s running (docker-compose up -d)';
                    } else if (data.error.includes('not configured')) {
                        errorMessage += '<br><br><strong>Configuration Required:</strong><br>';
                        errorMessage += '• Set SMTP Host and Port<br>';
                        errorMessage += '• Set From Address<br>';
                        errorMessage += '• Add recipients in Global Recipients or enable user notifications';
                    }

                    showAlert(errorMessage, 'danger', 'alert-email-test');
                }
            })
            .catch(error => {
                console.error('Error sending test email:', error);
                showAlert(`❌ Network error while sending test email: ${error.message}<br>
            <small class="text-muted">Check browser console for details</small>`, 'danger', 'alert-email-test');
            })
            .finally(() => {
                // Restore button state
                button.innerHTML = originalText;
                button.disabled = false;
            });
    }

    // Database Management Functions
    function resetDatabase() {
        if (confirm('⚠️ WARNING: This will reset the entire database and delete all documents. A backup will be created automatically. This action cannot be undone. Are you sure?')) {
            if (confirm('This is your final confirmation. Reset the database and delete all documents?')) {
                // Show loading state
                const button = event.target;
                const originalText = button.innerHTML;
                button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Resetting...';
                button.disabled = true;

                fetch('/api/database/reset', {
                    method: 'POST'
                })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            showAlert(`Success: ${data.message}`, 'success', 'alert-database-reset');
                            // Optionally redirect to documents page after a delay
                            setTimeout(() => {
                                window.location.href = '/documents';
                            }, 3000);
                        } else {
                            showAlert('Error: ' + data.message, 'danger', 'alert-database-reset');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        showAlert('An error occurred while resetting the database.', 'danger', 'alert-database-reset');
                    })
                    .finally(() => {
                        // Restore button state
                        button.innerHTML = originalText;
                        button.disabled = false;
                    });
            }
        }
    }

    // Enhanced Reset Functions
    function showEnhancedResetModal() {
        // Ensure Bootstrap is loaded and modal element exists
        const modalElement = document.getElementById('enhancedResetModal');
        if (!modalElement) {
            console.error('Modal element not found');
            return;
        }

        if (typeof bootstrap === 'undefined') {
            console.error('Bootstrap not loaded');
            return;
        }

        // Reset modal state
        updateResetPreview();
        validateResetOptions();

        // Get or create modal instance
        let modal = bootstrap.Modal.getInstance(modalElement);
        if (!modal) {
            modal = new bootstrap.Modal(modalElement);
        }

        // Show the modal
        modal.show();
    }

    function showCompleteResetModal() {
        // Ensure Bootstrap is loaded and modal element exists
        const modalElement = document.getElementById('enhancedResetModal');
        if (!modalElement) {
            console.error('Modal element not found');
            return;
        }

        if (typeof bootstrap === 'undefined') {
            console.error('Bootstrap not loaded');
            return;
        }

        // Get or create modal instance
        let modal = bootstrap.Modal.getInstance(modalElement);
        if (!modal) {
            modal = new bootstrap.Modal(modalElement);
        }

        // Show the modal first
        modal.show();

        // Pre-select all options after modal is shown (with a small delay to ensure DOM is ready)
        setTimeout(() => {
            selectAllReset();
            // Ensure validation runs after selection
            setTimeout(() => {
                validateResetOptions();
            }, 50);
        }, 200);
    }

    function updateResetPreview() {
        const options = getResetOptions();
        const preview = document.getElementById('resetPreview');

        if (!Object.values(options).some(v => v)) {
            preview.innerHTML = '<em class="text-muted">Select reset options above to see what will be reset...</em>';
            return;
        }

        let previewHtml = '<strong>The following will be reset:</strong><ul class="mt-2 mb-0">';

        if (options.reset_database) {
            previewHtml += '<li><i class="fas fa-database text-warning me-2"></i>Database and all revision documents</li>';
        }
        if (options.reset_repositories) {
            previewHtml += '<li><i class="fas fa-code-branch text-danger me-2"></i>All repository configurations and credentials</li>';
        }
        if (options.reset_users) {
            previewHtml += '<li><i class="fas fa-users text-danger me-2"></i>All user accounts and permissions</li>';
        }
        if (options.reset_email_config) {
            previewHtml += '<li><i class="fas fa-envelope text-warning me-2"></i>Email settings and recipient lists</li>';
        }
        if (options.reset_svn_config) {
            previewHtml += '<li><i class="fas fa-server text-warning me-2"></i>SVN/Git server settings and credentials</li>';
        }
        if (options.reset_ai_config) {
            previewHtml += '<li><i class="fas fa-robot text-warning me-2"></i>AI model configuration and settings</li>';
        }

        previewHtml += '</ul>';
        preview.innerHTML = previewHtml;
    }

    function validateResetOptions() {
        const options = getResetOptions();
        const safetyAlert = document.getElementById('safetyValidation');
        const safetyMessage = document.getElementById('safetyMessage');
        const executeBtn = document.getElementById('executeResetBtn');

        let warnings = [];
        let isValid = true;

        // Check for potentially problematic combinations
        if (options.reset_users && options.reset_email_config && !options.reset_repositories) {
            warnings.push('Resetting users and email config but keeping repositories may leave orphaned email references');
        }

        if (options.reset_svn_config && !options.reset_repositories) {
            warnings.push('Resetting SVN config but keeping repositories may break repository access');
        }

        if (options.reset_ai_config && !options.reset_database) {
            warnings.push('Resetting AI config without database reset may cause model compatibility issues');
        }

        // Check if at least one option is selected
        if (!Object.values(options).some(v => v)) {
            warnings.push('Please select at least one component to reset');
            isValid = false;
        }

        // Show warnings or hide safety alert
        if (warnings.length > 0) {
            safetyMessage.innerHTML = warnings.join('<br>• ');
            safetyAlert.style.display = 'block';
            safetyAlert.className = isValid ? 'alert alert-warning' : 'alert alert-danger';
        } else {
            safetyAlert.style.display = 'none';
        }

        // Enable/disable execute button
        executeBtn.disabled = !isValid;
    }

    function getResetOptions() {
        return {
            reset_database: document.getElementById('reset_database').checked,
            reset_repositories: document.getElementById('reset_repositories').checked,
            reset_users: document.getElementById('reset_users').checked,
            reset_email_config: document.getElementById('reset_email_config').checked,
            reset_svn_config: document.getElementById('reset_svn_config').checked,
            reset_ai_config: document.getElementById('reset_ai_config').checked
        };
    }

    function selectAllReset() {
        const checkboxes = ['reset_database', 'reset_repositories', 'reset_users', 'reset_email_config', 'reset_svn_config', 'reset_ai_config'];
        checkboxes.forEach(id => {
            const checkbox = document.getElementById(id);
            if (checkbox) {
                checkbox.checked = true;
            } else {
                console.warn(`Checkbox with id '${id}' not found`);
            }
        });
        updateResetPreview();
        validateResetOptions();
    }

    function selectNoneReset() {
        const checkboxes = ['reset_database', 'reset_repositories', 'reset_users', 'reset_email_config', 'reset_svn_config', 'reset_ai_config'];
        checkboxes.forEach(id => {
            const checkbox = document.getElementById(id);
            if (checkbox) {
                checkbox.checked = false;
            } else {
                console.warn(`Checkbox with id '${id}' not found`);
            }
        });
        updateResetPreview();
        validateResetOptions();
    }

    function executeEnhancedReset() {
        const options = getResetOptions();

        // Final confirmation
        const resetItems = [];
        if (options.reset_database) resetItems.push('Database & Documents');
        if (options.reset_repositories) resetItems.push('Repositories');
        if (options.reset_users) resetItems.push('Users');
        if (options.reset_email_config) resetItems.push('Email Settings');
        if (options.reset_svn_config) resetItems.push('SVN/Git Settings');
        if (options.reset_ai_config) resetItems.push('AI Settings');

        const confirmMessage = `⚠️ FINAL CONFIRMATION\n\nThis will reset: ${resetItems.join(', ')}\n\nBackups will be created automatically.\nThis action cannot be undone.\n\nProceed with reset?`;

        if (!confirm(confirmMessage)) {
            return;
        }

        // Show loading state
        const button = document.getElementById('executeResetBtn');
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Resetting...';
        button.disabled = true;

        // Execute the reset
        console.log('Executing reset with options:', options);
        fetch('/api/system/reset', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(options)
        })
            .then(response => {
                console.log('Reset response status:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('Reset response data:', data);
                if (data.success) {
                    showAlert(`✅ System Reset Complete: ${data.message}`, 'success', 'alert-system-reset');

                    // Close modal
                    const modalElement = document.getElementById('enhancedResetModal');
                    const modal = bootstrap.Modal.getInstance(modalElement);
                    if (modal) {
                        modal.hide();
                    }

                    // Redirect after delay
                    setTimeout(() => {
                        window.location.href = '/';
                    }, 3000);
                } else {
                    showAlert(`❌ Reset Failed: ${data.message}`, 'danger', 'alert-system-reset');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert('❌ An error occurred during system reset.', 'danger', 'alert-system-reset');
            })
            .finally(() => {
                // Restore button state
                button.innerHTML = originalText;
                button.disabled = false;
            });
    }

    // Add event listeners for reset option changes
    document.addEventListener('DOMContentLoaded', function () {
        const resetCheckboxes = ['reset_database', 'reset_repositories', 'reset_users', 'reset_email_config', 'reset_svn_config', 'reset_ai_config'];
        resetCheckboxes.forEach(id => {
            const checkbox = document.getElementById(id);
            if (checkbox) {
                checkbox.addEventListener('change', () => {
                    updateResetPreview();
                    validateResetOptions();
                });
            }
        });
    });

    // SQL Configuration Functions
    async function testSQLConnection(button) {
        const originalText = button.innerHTML;

        // Show loading state
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Testing...';
        button.disabled = true;

        try {
            // Get current form values for testing
            const formData = {
                sql_enabled: document.getElementById('sql_enabled')?.checked || false,
                sql_driver: document.getElementById('sql_driver')?.value || 'mysql',
                sql_host: document.getElementById('sql_host')?.value || 'mysql-test',
                sql_port: parseInt(document.getElementById('sql_port')?.value || '3306'),
                sql_database: document.getElementById('sql_database')?.value || 'change_requests',
                sql_username: document.getElementById('sql_username')?.value || 'reposense',
                sql_password: document.getElementById('sql_password')?.value || 'reposense123'
            };

            const response = await fetch('/api/sql/test-connection', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(formData)
            });

            const data = await response.json();

            if (data.success) {
                showAlert(`<strong>SQL Connection Successful!</strong><br>${data.message}`, 'success', 'alert-sql-test');
            } else {
                showAlert(`<strong>SQL Connection Failed!</strong><br>${data.message}`, 'danger', 'alert-sql-test');
            }
        } catch (error) {
            showAlert(`<strong>Connection Test Error!</strong><br>Failed to test SQL connection: ${error.message}`, 'danger', 'alert-sql-test');
        } finally {
            // Restore button state
            button.innerHTML = originalText;
            button.disabled = false;
        }
    }

    function toggleSQLConfig() {
        const enabled = document.getElementById('sql_enabled').checked;
        const configSection = document.getElementById('sql-config-section');

        if (enabled) {
            configSection.style.display = 'block';
        } else {
            configSection.style.display = 'none';
        }
    }

    function showSQLQueryEditor() {
        const query = document.getElementById('sql_query').value;
        const patterns = document.getElementById('sql_patterns').value;

        // Create modal for query editor
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.innerHTML = `
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title"><i class="fas fa-edit"></i> SQL Query Editor</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <label class="form-label">SQL Query Template</label>
                            <textarea class="form-control font-monospace" id="modal-sql-query" rows="6">${query}</textarea>
                            <div class="form-text">Use <code>:change_request_number</code> as parameter placeholder</div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Test Query</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="test-cr-number" placeholder="Enter CR number (e.g., 123)">
                                <button class="btn btn-outline-primary" onclick="testSQLQuery()">Test Query</button>
                            </div>
                        </div>
                        <div id="query-test-results"></div>
                        <div class="mb-3">
                            <label class="form-label">Extraction Patterns</label>
                            <textarea class="form-control font-monospace" id="modal-sql-patterns" rows="4">${patterns}</textarea>
                            <div class="form-text">Regular expressions to extract CR numbers (one per line)</div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-primary" onclick="saveSQLQuery()">Save Changes</button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        const bootstrapModal = new bootstrap.Modal(modal);
        bootstrapModal.show();

        // Clean up modal when closed
        modal.addEventListener('hidden.bs.modal', () => {
            document.body.removeChild(modal);
        });
    }

    function saveSQLQuery() {
        const query = document.getElementById('modal-sql-query').value;
        const patterns = document.getElementById('modal-sql-patterns').value;

        document.getElementById('sql_query').value = query;
        document.getElementById('sql_patterns').value = patterns;

        // Close modal
        const modal = document.querySelector('.modal.show');
        if (modal) {
            bootstrap.Modal.getInstance(modal).hide();
        }

        showAlert('SQL query and patterns updated successfully!', 'success', 'alert-sql-config');
    }

    async function testSQLQuery() {
        const query = document.getElementById('modal-sql-query').value;
        const crNumber = document.getElementById('test-cr-number').value;
        const resultsDiv = document.getElementById('query-test-results');

        if (!crNumber) {
            resultsDiv.innerHTML = '<div class="alert alert-warning">Please enter a change request number to test</div>';
            return;
        }

        resultsDiv.innerHTML = '<div class="alert alert-info"><i class="fas fa-spinner fa-spin"></i> Testing query...</div>';

        try {
            const formData = {
                sql_enabled: true,
                sql_driver: document.getElementById('sql_driver')?.value || 'mysql',
                sql_host: document.getElementById('sql_host')?.value || 'mysql-test',
                sql_port: parseInt(document.getElementById('sql_port')?.value || '3306'),
                sql_database: document.getElementById('sql_database')?.value || 'change_requests',
                sql_username: document.getElementById('sql_username')?.value || 'reposense',
                sql_password: document.getElementById('sql_password')?.value || 'reposense123',
                sql_query: query,
                test_cr_number: crNumber
            };

            const response = await fetch('/api/sql/test-query', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(formData)
            });

            const data = await response.json();

            if (data.success) {
                if (data.result) {
                    resultsDiv.innerHTML = `
                        <div class="alert alert-success">
                            <strong>Query Successful!</strong><br>
                            Found change request: <strong>${data.result.title}</strong><br>
                            Priority: ${data.result.priority} | Status: ${data.result.status}
                        </div>
                    `;
                } else {
                    resultsDiv.innerHTML = '<div class="alert alert-warning">Query executed successfully but no results found</div>';
                }
            } else {
                resultsDiv.innerHTML = `<div class="alert alert-danger"><strong>Query Failed:</strong><br>${data.message}</div>`;
            }
        } catch (error) {
            resultsDiv.innerHTML = `<div class="alert alert-danger"><strong>Test Error:</strong><br>${error.message}</div>`;
        }
    }

    // Initialize SQL settings visibility on page load
    document.addEventListener('DOMContentLoaded', function () {
        // Add SQL toggle listener
        const sqlEnabledCheckbox = document.getElementById('sql_enabled');
        if (sqlEnabledCheckbox) {
            sqlEnabledCheckbox.addEventListener('change', toggleSQLConfig);
        }
    });


</script>
{% endblock %}